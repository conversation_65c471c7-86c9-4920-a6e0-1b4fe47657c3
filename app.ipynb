{"cells": [{"cell_type": "code", "execution_count": null, "id": "f8e7985a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "36929938", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Listening to topic: es_created_File\n", "payload=FilePayload(file_name='6bb98886ce1a010290d1f377c4c94414', es_id='6bb98886ce1a010290d1f377c4c94414', file_type='image')\n", "{'_id': '6bb98886ce1a010290d1f377c4c94414', 'mimeType': 'image/jpeg'}\n", "payload=FilePayload(file_name='3131b5059183b5bc0ec5089f3465ca94', es_id='3131b5059183b5bc0ec5089f3465ca94', file_type='image')\n", "{'_id': '3131b5059183b5bc0ec5089f3465ca94', 'mimeType': 'image/jpeg'}\n", "payload=FilePayload(file_name='59cc4077d360ec9ca0f1a8411cecebf4', es_id='59cc4077d360ec9ca0f1a8411cecebf4', file_type='image')\n", "{'_id': '59cc4077d360ec9ca0f1a8411cecebf4', 'mimeType': 'image/jpeg'}\n", "payload=FilePayload(file_name='44c19557fd9ac38150b6cdee43eef78a', es_id='44c19557fd9ac38150b6cdee43eef78a', file_type='image')\n", "{'_id': '44c19557fd9ac38150b6cdee43eef78a', 'mimeType': 'image/jpeg'}\n", "payload=FilePayload(file_name='93a9e03097208657aed374e183ea33dd', es_id='93a9e03097208657aed374e183ea33dd', file_type='image')\n", "{'_id': '93a9e03097208657aed374e183ea33dd', 'mimeType': 'image/jpeg'}\n", "payload=FilePayload(file_name='270cf44b48f5960bb43c584ce48e834e', es_id='270cf44b48f5960bb43c584ce48e834e', file_type='image')\n", "{'_id': '270cf44b48f5960bb43c584ce48e834e', 'mimeType': 'image/jpeg'}\n", "payload=FilePayload(file_name='75822bb648f8e4c5e392f7765675ee55', es_id='75822bb648f8e4c5e392f7765675ee55', file_type='image')\n", "{'_id': '75822bb648f8e4c5e392f7765675ee55', 'mimeType': 'image/jpeg'}\n", "payload=FilePayload(file_name='0ca97d8941a73be2141c7f5e1f887f62', es_id='0ca97d8941a73be2141c7f5e1f887f62', file_type='image')\n", "{'_id': '0ca97d8941a73be2141c7f5e1f887f62', 'mimeType': 'image/jpeg'}\n", "payload=FilePayload(file_name='241d555723a4fcf1f725c94a7f456145', es_id='241d555723a4fcf1f725c94a7f456145', file_type='image')\n", "{'_id': '241d555723a4fcf1f725c94a7f456145', 'mimeType': 'image/jpeg'}\n", "payload=FilePayload(file_name='71c7071c2cb61c4be003b63682f21c09', es_id='71c7071c2cb61c4be003b63682f21c09', file_type='image')\n", "{'_id': '71c7071c2cb61c4be003b63682f21c09', 'mimeType': 'image/jpeg'}\n", "payload=FilePayload(file_name='8227301e789c40ddd10922195e0dda6d', es_id='8227301e789c40ddd10922195e0dda6d', file_type='image')\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mKeyboardInterrupt\u001b[39m                         <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[21]\u001b[39m\u001b[32m, line 76\u001b[39m\n\u001b[32m     72\u001b[39m         \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m data \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m data.payload.es_id:\n\u001b[32m     73\u001b[39m             \u001b[38;5;28;01mcontinue\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m76\u001b[39m         file_document = \u001b[43mget_document\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdata\u001b[49m\u001b[43m.\u001b[49m\u001b[43mpayload\u001b[49m\u001b[43m.\u001b[49m\u001b[43mes_id\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     77\u001b[39m         \u001b[38;5;28mprint\u001b[39m(file_document)\n\u001b[32m     78\u001b[39m \u001b[38;5;66;03m# else:\u001b[39;00m\n\u001b[32m     79\u001b[39m \u001b[38;5;66;03m#     print(\"Kafka is disabled.\")\u001b[39;00m\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[21]\u001b[39m\u001b[32m, line 35\u001b[39m, in \u001b[36mget_document\u001b[39m\u001b[34m(file_id, download)\u001b[39m\n\u001b[32m     33\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m download:\n\u001b[32m     34\u001b[39m     \u001b[38;5;28;01mwith\u001b[39;00m Client(base_url=DOCSERVER_V2_BASEURL) \u001b[38;5;28;01mas\u001b[39;00m client:\n\u001b[32m---> \u001b[39m\u001b[32m35\u001b[39m         response = \u001b[43mclient\u001b[49m\u001b[43m.\u001b[49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43mf\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43m/files/\u001b[39;49m\u001b[38;5;132;43;01m{\u001b[39;49;00m\u001b[43mfile_id\u001b[49m\u001b[38;5;132;43;01m}\u001b[39;49;00m\u001b[33;43m?fieldsToReturn=\u001b[39;49m\u001b[38;5;132;43;01m{\u001b[39;49;00m\u001b[43mfieldsToReturn\u001b[49m\u001b[38;5;132;43;01m}\u001b[39;49;00m\u001b[33;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[32m     36\u001b[39m         \u001b[38;5;28;01mreturn\u001b[39;00m response.json()\n\u001b[32m     38\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m Client(base_url=DOCSERVER_V2_BASEURL, follow_redirects=\u001b[38;5;28;01mTrue\u001b[39;00m) \u001b[38;5;28;01mas\u001b[39;00m client:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/face-validation/.venv/lib/python3.13/site-packages/httpx/_client.py:1053\u001b[39m, in \u001b[36mClient.get\u001b[39m\u001b[34m(self, url, params, headers, cookies, auth, follow_redirects, timeout, extensions)\u001b[39m\n\u001b[32m   1036\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mget\u001b[39m(\n\u001b[32m   1037\u001b[39m     \u001b[38;5;28mself\u001b[39m,\n\u001b[32m   1038\u001b[39m     url: URL | \u001b[38;5;28mstr\u001b[39m,\n\u001b[32m   (...)\u001b[39m\u001b[32m   1046\u001b[39m     extensions: RequestExtensions | \u001b[38;5;28;01mNone\u001b[39;00m = \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[32m   1047\u001b[39m ) -> Response:\n\u001b[32m   1048\u001b[39m \u001b[38;5;250m    \u001b[39m\u001b[33;03m\"\"\"\u001b[39;00m\n\u001b[32m   1049\u001b[39m \u001b[33;03m    Send a `GET` request.\u001b[39;00m\n\u001b[32m   1050\u001b[39m \n\u001b[32m   1051\u001b[39m \u001b[33;03m    **Parameters**: See `httpx.request`.\u001b[39;00m\n\u001b[32m   1052\u001b[39m \u001b[33;03m    \"\"\"\u001b[39;00m\n\u001b[32m-> \u001b[39m\u001b[32m1053\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   1054\u001b[39m \u001b[43m        \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mGET\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m   1055\u001b[39m \u001b[43m        \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1056\u001b[39m \u001b[43m        \u001b[49m\u001b[43mparams\u001b[49m\u001b[43m=\u001b[49m\u001b[43mparams\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1057\u001b[39m \u001b[43m        \u001b[49m\u001b[43mheaders\u001b[49m\u001b[43m=\u001b[49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1058\u001b[39m \u001b[43m        \u001b[49m\u001b[43mcookies\u001b[49m\u001b[43m=\u001b[49m\u001b[43mcookies\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1059\u001b[39m \u001b[43m        \u001b[49m\u001b[43mauth\u001b[49m\u001b[43m=\u001b[49m\u001b[43mauth\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1060\u001b[39m \u001b[43m        \u001b[49m\u001b[43mfollow_redirects\u001b[49m\u001b[43m=\u001b[49m\u001b[43mfollow_redirects\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1061\u001b[39m \u001b[43m        \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m=\u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1062\u001b[39m \u001b[43m        \u001b[49m\u001b[43mextensions\u001b[49m\u001b[43m=\u001b[49m\u001b[43mextensions\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1063\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/face-validation/.venv/lib/python3.13/site-packages/httpx/_client.py:825\u001b[39m, in \u001b[36mClient.request\u001b[39m\u001b[34m(self, method, url, content, data, files, json, params, headers, cookies, auth, follow_redirects, timeout, extensions)\u001b[39m\n\u001b[32m    810\u001b[39m     warnings.warn(message, \u001b[38;5;167;01mDeprecationWarning\u001b[39;00m, stacklevel=\u001b[32m2\u001b[39m)\n\u001b[32m    812\u001b[39m request = \u001b[38;5;28mself\u001b[39m.build_request(\n\u001b[32m    813\u001b[39m     method=method,\n\u001b[32m    814\u001b[39m     url=url,\n\u001b[32m   (...)\u001b[39m\u001b[32m    823\u001b[39m     extensions=extensions,\n\u001b[32m    824\u001b[39m )\n\u001b[32m--> \u001b[39m\u001b[32m825\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43msend\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mauth\u001b[49m\u001b[43m=\u001b[49m\u001b[43mauth\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mfollow_redirects\u001b[49m\u001b[43m=\u001b[49m\u001b[43mfollow_redirects\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/face-validation/.venv/lib/python3.13/site-packages/httpx/_client.py:914\u001b[39m, in \u001b[36mClient.send\u001b[39m\u001b[34m(self, request, stream, auth, follow_redirects)\u001b[39m\n\u001b[32m    910\u001b[39m \u001b[38;5;28mself\u001b[39m._set_timeout(request)\n\u001b[32m    912\u001b[39m auth = \u001b[38;5;28mself\u001b[39m._build_request_auth(request, auth)\n\u001b[32m--> \u001b[39m\u001b[32m914\u001b[39m response = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_send_handling_auth\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    915\u001b[39m \u001b[43m    \u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    916\u001b[39m \u001b[43m    \u001b[49m\u001b[43mauth\u001b[49m\u001b[43m=\u001b[49m\u001b[43mauth\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    917\u001b[39m \u001b[43m    \u001b[49m\u001b[43mfollow_redirects\u001b[49m\u001b[43m=\u001b[49m\u001b[43mfollow_redirects\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    918\u001b[39m \u001b[43m    \u001b[49m\u001b[43mhistory\u001b[49m\u001b[43m=\u001b[49m\u001b[43m[\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    919\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    920\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m    921\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m stream:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/face-validation/.venv/lib/python3.13/site-packages/httpx/_client.py:942\u001b[39m, in \u001b[36mClient._send_handling_auth\u001b[39m\u001b[34m(self, request, auth, follow_redirects, history)\u001b[39m\n\u001b[32m    939\u001b[39m request = \u001b[38;5;28mnext\u001b[39m(auth_flow)\n\u001b[32m    941\u001b[39m \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m942\u001b[39m     response = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_send_handling_redirects\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    943\u001b[39m \u001b[43m        \u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    944\u001b[39m \u001b[43m        \u001b[49m\u001b[43mfollow_redirects\u001b[49m\u001b[43m=\u001b[49m\u001b[43mfollow_redirects\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    945\u001b[39m \u001b[43m        \u001b[49m\u001b[43mhistory\u001b[49m\u001b[43m=\u001b[49m\u001b[43mhistory\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    946\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    947\u001b[39m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m    948\u001b[39m         \u001b[38;5;28;01mtry\u001b[39;00m:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/face-validation/.venv/lib/python3.13/site-packages/httpx/_client.py:979\u001b[39m, in \u001b[36mClient._send_handling_redirects\u001b[39m\u001b[34m(self, request, follow_redirects, history)\u001b[39m\n\u001b[32m    976\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m hook \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m._event_hooks[\u001b[33m\"\u001b[39m\u001b[33mrequest\u001b[39m\u001b[33m\"\u001b[39m]:\n\u001b[32m    977\u001b[39m     hook(request)\n\u001b[32m--> \u001b[39m\u001b[32m979\u001b[39m response = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_send_single_request\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    980\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m    981\u001b[39m     \u001b[38;5;28;01mfor\u001b[39;00m hook \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m._event_hooks[\u001b[33m\"\u001b[39m\u001b[33mresponse\u001b[39m\u001b[33m\"\u001b[39m]:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/face-validation/.venv/lib/python3.13/site-packages/httpx/_client.py:1014\u001b[39m, in \u001b[36mClient._send_single_request\u001b[39m\u001b[34m(self, request)\u001b[39m\n\u001b[32m   1009\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mRuntimeError\u001b[39;00m(\n\u001b[32m   1010\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33mAttempted to send an async request with a sync Client instance.\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m   1011\u001b[39m     )\n\u001b[32m   1013\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m request_context(request=request):\n\u001b[32m-> \u001b[39m\u001b[32m1014\u001b[39m     response = \u001b[43mtransport\u001b[49m\u001b[43m.\u001b[49m\u001b[43mhandle_request\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1016\u001b[39m \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(response.stream, SyncByteStream)\n\u001b[32m   1018\u001b[39m response.request = request\n", "\u001b[36mFile \u001b[39m\u001b[32m~/face-validation/.venv/lib/python3.13/site-packages/httpx/_transports/default.py:250\u001b[39m, in \u001b[36mHTTPTransport.handle_request\u001b[39m\u001b[34m(self, request)\u001b[39m\n\u001b[32m    237\u001b[39m req = httpcore.Request(\n\u001b[32m    238\u001b[39m     method=request.method,\n\u001b[32m    239\u001b[39m     url=httpcore.URL(\n\u001b[32m   (...)\u001b[39m\u001b[32m    247\u001b[39m     extensions=request.extensions,\n\u001b[32m    248\u001b[39m )\n\u001b[32m    249\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m map_httpcore_exceptions():\n\u001b[32m--> \u001b[39m\u001b[32m250\u001b[39m     resp = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_pool\u001b[49m\u001b[43m.\u001b[49m\u001b[43mhandle_request\u001b[49m\u001b[43m(\u001b[49m\u001b[43mreq\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    252\u001b[39m \u001b[38;5;28;01massert\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(resp.stream, typing.Iterable)\n\u001b[32m    254\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m Response(\n\u001b[32m    255\u001b[39m     status_code=resp.status,\n\u001b[32m    256\u001b[39m     headers=resp.headers,\n\u001b[32m    257\u001b[39m     stream=ResponseStream(resp.stream),\n\u001b[32m    258\u001b[39m     extensions=resp.extensions,\n\u001b[32m    259\u001b[39m )\n", "\u001b[36mFile \u001b[39m\u001b[32m~/face-validation/.venv/lib/python3.13/site-packages/httpcore/_sync/connection_pool.py:256\u001b[39m, in \u001b[36mConnectionPool.handle_request\u001b[39m\u001b[34m(self, request)\u001b[39m\n\u001b[32m    253\u001b[39m         closing = \u001b[38;5;28mself\u001b[39m._assign_requests_to_connections()\n\u001b[32m    255\u001b[39m     \u001b[38;5;28mself\u001b[39m._close_connections(closing)\n\u001b[32m--> \u001b[39m\u001b[32m256\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m exc \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mN<PERSON>\u001b[39;00m\n\u001b[32m    258\u001b[39m \u001b[38;5;66;03m# Return the response. Note that in this case we still have to manage\u001b[39;00m\n\u001b[32m    259\u001b[39m \u001b[38;5;66;03m# the point at which the response is closed.\u001b[39;00m\n\u001b[32m    260\u001b[39m \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(response.stream, typing.Iterable)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/face-validation/.venv/lib/python3.13/site-packages/httpcore/_sync/connection_pool.py:236\u001b[39m, in \u001b[36mConnectionPool.handle_request\u001b[39m\u001b[34m(self, request)\u001b[39m\n\u001b[32m    232\u001b[39m connection = pool_request.wait_for_connection(timeout=timeout)\n\u001b[32m    234\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m    235\u001b[39m     \u001b[38;5;66;03m# Send the request on the assigned connection.\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m236\u001b[39m     response = \u001b[43mconnection\u001b[49m\u001b[43m.\u001b[49m\u001b[43mhandle_request\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    237\u001b[39m \u001b[43m        \u001b[49m\u001b[43mpool_request\u001b[49m\u001b[43m.\u001b[49m\u001b[43mrequest\u001b[49m\n\u001b[32m    238\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    239\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m ConnectionNotAvailable:\n\u001b[32m    240\u001b[39m     \u001b[38;5;66;03m# In some cases a connection may initially be available to\u001b[39;00m\n\u001b[32m    241\u001b[39m     \u001b[38;5;66;03m# handle a request, but then become unavailable.\u001b[39;00m\n\u001b[32m    242\u001b[39m     \u001b[38;5;66;03m#\u001b[39;00m\n\u001b[32m    243\u001b[39m     \u001b[38;5;66;03m# In this case we clear the connection and try again.\u001b[39;00m\n\u001b[32m    244\u001b[39m     pool_request.clear_connection()\n", "\u001b[36mFile \u001b[39m\u001b[32m~/face-validation/.venv/lib/python3.13/site-packages/httpcore/_sync/connection.py:103\u001b[39m, in \u001b[36mHTTPConnection.handle_request\u001b[39m\u001b[34m(self, request)\u001b[39m\n\u001b[32m    100\u001b[39m     \u001b[38;5;28mself\u001b[39m._connect_failed = \u001b[38;5;28;01mTrue\u001b[39;00m\n\u001b[32m    101\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m exc\n\u001b[32m--> \u001b[39m\u001b[32m103\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_connection\u001b[49m\u001b[43m.\u001b[49m\u001b[43mhandle_request\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/face-validation/.venv/lib/python3.13/site-packages/httpcore/_sync/http11.py:136\u001b[39m, in \u001b[36mHTTP11Connection.handle_request\u001b[39m\u001b[34m(self, request)\u001b[39m\n\u001b[32m    134\u001b[39m     \u001b[38;5;28;01mwith\u001b[39;00m Trace(\u001b[33m\"\u001b[39m\u001b[33mresponse_closed\u001b[39m\u001b[33m\"\u001b[39m, logger, request) \u001b[38;5;28;01mas\u001b[39;00m trace:\n\u001b[32m    135\u001b[39m         \u001b[38;5;28mself\u001b[39m._response_closed()\n\u001b[32m--> \u001b[39m\u001b[32m136\u001b[39m \u001b[38;5;28;01mraise\u001b[39;00m exc\n", "\u001b[36mFile \u001b[39m\u001b[32m~/face-validation/.venv/lib/python3.13/site-packages/httpcore/_sync/http11.py:106\u001b[39m, in \u001b[36mHTTP11Connection.handle_request\u001b[39m\u001b[34m(self, request)\u001b[39m\n\u001b[32m     95\u001b[39m     \u001b[38;5;28;01mpass\u001b[39;00m\n\u001b[32m     97\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m Trace(\n\u001b[32m     98\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33mreceive_response_headers\u001b[39m\u001b[33m\"\u001b[39m, logger, request, kwargs\n\u001b[32m     99\u001b[39m ) \u001b[38;5;28;01mas\u001b[39;00m trace:\n\u001b[32m    100\u001b[39m     (\n\u001b[32m    101\u001b[39m         http_version,\n\u001b[32m    102\u001b[39m         status,\n\u001b[32m    103\u001b[39m         reason_phrase,\n\u001b[32m    104\u001b[39m         headers,\n\u001b[32m    105\u001b[39m         trailing_data,\n\u001b[32m--> \u001b[39m\u001b[32m106\u001b[39m     ) = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_receive_response_headers\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    107\u001b[39m     trace.return_value = (\n\u001b[32m    108\u001b[39m         http_version,\n\u001b[32m    109\u001b[39m         status,\n\u001b[32m    110\u001b[39m         reason_phrase,\n\u001b[32m    111\u001b[39m         headers,\n\u001b[32m    112\u001b[39m     )\n\u001b[32m    114\u001b[39m network_stream = \u001b[38;5;28mself\u001b[39m._network_stream\n", "\u001b[36mFile \u001b[39m\u001b[32m~/face-validation/.venv/lib/python3.13/site-packages/httpcore/_sync/http11.py:177\u001b[39m, in \u001b[36mHTTP11Connection._receive_response_headers\u001b[39m\u001b[34m(self, request)\u001b[39m\n\u001b[32m    174\u001b[39m timeout = timeouts.get(\u001b[33m\"\u001b[39m\u001b[33mread\u001b[39m\u001b[33m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m)\n\u001b[32m    176\u001b[39m \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m177\u001b[39m     event = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_receive_event\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m=\u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    178\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(event, h11.Response):\n\u001b[32m    179\u001b[39m         \u001b[38;5;28;01mbreak\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/face-validation/.venv/lib/python3.13/site-packages/httpcore/_sync/http11.py:217\u001b[39m, in \u001b[36mHTTP11Connection._receive_event\u001b[39m\u001b[34m(self, timeout)\u001b[39m\n\u001b[32m    214\u001b[39m     event = \u001b[38;5;28mself\u001b[39m._h11_state.next_event()\n\u001b[32m    216\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m event \u001b[38;5;129;01mis\u001b[39;00m h11.NEED_DATA:\n\u001b[32m--> \u001b[39m\u001b[32m217\u001b[39m     data = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_network_stream\u001b[49m\u001b[43m.\u001b[49m\u001b[43mread\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    218\u001b[39m \u001b[43m        \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mREAD_NUM_BYTES\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m=\u001b[49m\u001b[43mtimeout\u001b[49m\n\u001b[32m    219\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    221\u001b[39m     \u001b[38;5;66;03m# If we feed this case through h11 we'll raise an exception like:\u001b[39;00m\n\u001b[32m    222\u001b[39m     \u001b[38;5;66;03m#\u001b[39;00m\n\u001b[32m    223\u001b[39m     \u001b[38;5;66;03m#     httpcore.RemoteProtocolError: can't handle event type\u001b[39;00m\n\u001b[32m   (...)\u001b[39m\u001b[32m    227\u001b[39m     \u001b[38;5;66;03m# perspective. Instead we handle this case distinctly and treat\u001b[39;00m\n\u001b[32m    228\u001b[39m     \u001b[38;5;66;03m# it as a ConnectError.\u001b[39;00m\n\u001b[32m    229\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m data == \u001b[33mb\u001b[39m\u001b[33m\"\u001b[39m\u001b[33m\"\u001b[39m \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28mself\u001b[39m._h11_state.their_state == h11.SEND_RESPONSE:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/face-validation/.venv/lib/python3.13/site-packages/httpcore/_backends/sync.py:128\u001b[39m, in \u001b[36mSyncStream.read\u001b[39m\u001b[34m(self, max_bytes, timeout)\u001b[39m\n\u001b[32m    126\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m map_exceptions(exc_map):\n\u001b[32m    127\u001b[39m     \u001b[38;5;28mself\u001b[39m._sock.settimeout(timeout)\n\u001b[32m--> \u001b[39m\u001b[32m128\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_sock\u001b[49m\u001b[43m.\u001b[49m\u001b[43mrecv\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmax_bytes\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[31mKeyboardInterrupt\u001b[39m: "]}], "source": ["import os\n", "from kafka import KafkaConsumer\n", "from httpx import Client\n", "# <PERSON><PERSON> config from environment variables\n", "KAFKA_ENABLED = os.getenv(\"KAFKA_ENABLED\", \"true\").lower() == \"true\"\n", "KAFKA_URL = os.getenv(\"KAFKA_URL\", \"kafka.default.svc.cluster.local:9092\")\n", "KAFKA_TOPIC_ES_CREATED_FILE = os.getenv(\"KAFKA_TOPIC_ES_CREATED_FILE\", \"es_created_File\")\n", "KAFKA_CONSUMER_GROUP = os.getenv(\"KAFKA_CONSUMER_GROUP\", \"face-validation\")\n", "KAFKA_AUTO_OFFSET_RESET = os.getenv(\"KAFKA_AUTO_OFFSET_RESET\", \"latest\")\n", "DOCSERVER_V2_BASEURL = os.getenv(\"DOCSERVER_V2_URL\", \"http://cyber-docserver-v2-microservice.cyber.svc.cluster.local\")\n", "\n", "from pydantic import BaseModel, Field\n", "from typing import Optional\n", "import json\n", "\n", "class FilePayload(BaseModel):\n", "    file_name: Optional[str] = Field(None, alias=\"fileName\")\n", "    es_id: Optional[str] = Field(None, alias=\"es-id\")\n", "    file_type: Optional[str] = Field(None, alias=\"fileType\")\n", "\n", "\n", "class FileMessage(BaseModel):\n", "    payload: FilePayload\n", "\n", "\n", "def get_document(file_id: str, download: bool = False):\n", "    fieldsToReturn = \"mimeType\"\n", "    # if download:\n", "    #     download=\"download=true&return_url=true\"\n", "    \n", "    # http://cyber-docserver-v2-microservice.cyber.svc.cluster.local/files/f75a7727163d803ee1b981f6e9487dfc?download=false&fieldsToReturn=mimeType&return_url=false\n", "\n", "    if not download:\n", "        with Client(base_url=DOCSERVER_V2_BASEURL) as client:\n", "            response = client.get(f\"/files/{file_id}?fieldsToReturn={fieldsToReturn}\")\n", "            return response.json()\n", "        \n", "    with Client(base_url=DOCSERVER_V2_BASEURL, follow_redirects=True) as client:\n", "        response = client.get(f\"/files/{file_id}?download={download}&fieldsToReturn={fieldsToReturn}\")\n", "        return response.content\n", "\n", "image_mimetypes = [\n", "    \"image/jpeg\",  # Standard JPEG image\n", "    \"image/pjpeg\", # Progressive JPEG (less common)\n", "    \"image/x-png\", # PNG image (alternative MIME type)\n", "    \"image/png\"    # Standard PNG image\n", "]\n", "\n", "\n", "\n", "if KAFKA_ENABLED:\n", "    consumer = KafkaConsumer(\n", "        KAFKA_TOPIC_ES_CREATED_FILE,\n", "        bootstrap_servers=KAFKA_URL,\n", "        group_id=KAFKA_CONSUMER_GROUP,\n", "        auto_offset_reset=KAFKA_AUTO_OFFSET_RESET,\n", "        enable_auto_commit=True,\n", "        value_deserializer=lambda x: x.decode(\"utf-8\"),\n", "    )\n", "\n", "\n", "    print(f\"Listening to topic: {KAFKA_TOPIC_ES_CREATED_FILE}\")\n", "\n", "    for message in consumer:\n", "        msg = message.value\n", "        data = None\n", "        try:\n", "            data = FileMessage(**json.loads(msg))\n", "            print(data)\n", "        except Exception:\n", "            pass\n", "        \n", "        if not data or not data.payload.es_id:\n", "            continue\n", "        \n", "        \n", "        if data.payload.file_type == \"image\":\n", "            file_bytes = get_document(data.payload.es_id, download=True)\n", "            \n", "            # Todo PROCESS THIS \n", "\n"]}, {"cell_type": "code", "execution_count": 23, "id": "6d78a86f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Listening to topic: es_created_File\n", "payload=FilePayload(file_name='6c06aaf0cb3fef793b61f1635b2f46b6', es_id='6c06aaf0cb3fef793b61f1635b2f46b6', file_type='image')\n", "{'_id': '6c06aaf0cb3fef793b61f1635b2f46b6', 'mimeType': 'image/jpeg'}\n", "payload=FilePayload(file_name='64a0244941e305e2b6c5b109888d8cde', es_id='64a0244941e305e2b6c5b109888d8cde', file_type='image')\n", "{'_id': '64a0244941e305e2b6c5b109888d8cde', 'mimeType': 'image/jpeg'}\n", "payload=FilePayload(file_name='40d8a4ba2b80c1c72fb649ab8c356985', es_id='40d8a4ba2b80c1c72fb649ab8c356985', file_type='image')\n", "{'_id': '40d8a4ba2b80c1c72fb649ab8c356985', 'mimeType': 'image/jpeg'}\n", "payload=FilePayload(file_name='b7e85eca1b7e3da531e8456e21526f42', es_id='b7e85eca1b7e3da531e8456e21526f42', file_type='image')\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mKeyboardInterrupt\u001b[39m                         <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[23]\u001b[39m\u001b[32m, line 16\u001b[39m\n\u001b[32m     12\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m data \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m data.payload.es_id:\n\u001b[32m     13\u001b[39m         \u001b[38;5;28;01mcontinue\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m16\u001b[39m     file_document = \u001b[43mget_document\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdata\u001b[49m\u001b[43m.\u001b[49m\u001b[43mpayload\u001b[49m\u001b[43m.\u001b[49m\u001b[43mes_id\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     17\u001b[39m     \u001b[38;5;28mprint\u001b[39m(file_document)\n\u001b[32m     18\u001b[39m \u001b[38;5;66;03m# else:\u001b[39;00m\n\u001b[32m     19\u001b[39m \u001b[38;5;66;03m#     print(\"Kafka is disabled.\")\u001b[39;00m\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[21]\u001b[39m\u001b[32m, line 35\u001b[39m, in \u001b[36mget_document\u001b[39m\u001b[34m(file_id, download)\u001b[39m\n\u001b[32m     33\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m download:\n\u001b[32m     34\u001b[39m     \u001b[38;5;28;01mwith\u001b[39;00m Client(base_url=DOCSERVER_V2_BASEURL) \u001b[38;5;28;01mas\u001b[39;00m client:\n\u001b[32m---> \u001b[39m\u001b[32m35\u001b[39m         response = \u001b[43mclient\u001b[49m\u001b[43m.\u001b[49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43mf\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43m/files/\u001b[39;49m\u001b[38;5;132;43;01m{\u001b[39;49;00m\u001b[43mfile_id\u001b[49m\u001b[38;5;132;43;01m}\u001b[39;49;00m\u001b[33;43m?fieldsToReturn=\u001b[39;49m\u001b[38;5;132;43;01m{\u001b[39;49;00m\u001b[43mfieldsToReturn\u001b[49m\u001b[38;5;132;43;01m}\u001b[39;49;00m\u001b[33;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[32m     36\u001b[39m         \u001b[38;5;28;01mreturn\u001b[39;00m response.json()\n\u001b[32m     38\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m Client(base_url=DOCSERVER_V2_BASEURL, follow_redirects=\u001b[38;5;28;01mTrue\u001b[39;00m) \u001b[38;5;28;01mas\u001b[39;00m client:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/face-validation/.venv/lib/python3.13/site-packages/httpx/_client.py:1053\u001b[39m, in \u001b[36mClient.get\u001b[39m\u001b[34m(self, url, params, headers, cookies, auth, follow_redirects, timeout, extensions)\u001b[39m\n\u001b[32m   1036\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mget\u001b[39m(\n\u001b[32m   1037\u001b[39m     \u001b[38;5;28mself\u001b[39m,\n\u001b[32m   1038\u001b[39m     url: URL | \u001b[38;5;28mstr\u001b[39m,\n\u001b[32m   (...)\u001b[39m\u001b[32m   1046\u001b[39m     extensions: RequestExtensions | \u001b[38;5;28;01mNone\u001b[39;00m = \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[32m   1047\u001b[39m ) -> Response:\n\u001b[32m   1048\u001b[39m \u001b[38;5;250m    \u001b[39m\u001b[33;03m\"\"\"\u001b[39;00m\n\u001b[32m   1049\u001b[39m \u001b[33;03m    Send a `GET` request.\u001b[39;00m\n\u001b[32m   1050\u001b[39m \n\u001b[32m   1051\u001b[39m \u001b[33;03m    **Parameters**: See `httpx.request`.\u001b[39;00m\n\u001b[32m   1052\u001b[39m \u001b[33;03m    \"\"\"\u001b[39;00m\n\u001b[32m-> \u001b[39m\u001b[32m1053\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   1054\u001b[39m \u001b[43m        \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mGET\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m   1055\u001b[39m \u001b[43m        \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1056\u001b[39m \u001b[43m        \u001b[49m\u001b[43mparams\u001b[49m\u001b[43m=\u001b[49m\u001b[43mparams\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1057\u001b[39m \u001b[43m        \u001b[49m\u001b[43mheaders\u001b[49m\u001b[43m=\u001b[49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1058\u001b[39m \u001b[43m        \u001b[49m\u001b[43mcookies\u001b[49m\u001b[43m=\u001b[49m\u001b[43mcookies\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1059\u001b[39m \u001b[43m        \u001b[49m\u001b[43mauth\u001b[49m\u001b[43m=\u001b[49m\u001b[43mauth\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1060\u001b[39m \u001b[43m        \u001b[49m\u001b[43mfollow_redirects\u001b[49m\u001b[43m=\u001b[49m\u001b[43mfollow_redirects\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1061\u001b[39m \u001b[43m        \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m=\u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1062\u001b[39m \u001b[43m        \u001b[49m\u001b[43mextensions\u001b[49m\u001b[43m=\u001b[49m\u001b[43mextensions\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1063\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/face-validation/.venv/lib/python3.13/site-packages/httpx/_client.py:825\u001b[39m, in \u001b[36mClient.request\u001b[39m\u001b[34m(self, method, url, content, data, files, json, params, headers, cookies, auth, follow_redirects, timeout, extensions)\u001b[39m\n\u001b[32m    810\u001b[39m     warnings.warn(message, \u001b[38;5;167;01mDeprecationWarning\u001b[39;00m, stacklevel=\u001b[32m2\u001b[39m)\n\u001b[32m    812\u001b[39m request = \u001b[38;5;28mself\u001b[39m.build_request(\n\u001b[32m    813\u001b[39m     method=method,\n\u001b[32m    814\u001b[39m     url=url,\n\u001b[32m   (...)\u001b[39m\u001b[32m    823\u001b[39m     extensions=extensions,\n\u001b[32m    824\u001b[39m )\n\u001b[32m--> \u001b[39m\u001b[32m825\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43msend\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mauth\u001b[49m\u001b[43m=\u001b[49m\u001b[43mauth\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mfollow_redirects\u001b[49m\u001b[43m=\u001b[49m\u001b[43mfollow_redirects\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/face-validation/.venv/lib/python3.13/site-packages/httpx/_client.py:914\u001b[39m, in \u001b[36mClient.send\u001b[39m\u001b[34m(self, request, stream, auth, follow_redirects)\u001b[39m\n\u001b[32m    910\u001b[39m \u001b[38;5;28mself\u001b[39m._set_timeout(request)\n\u001b[32m    912\u001b[39m auth = \u001b[38;5;28mself\u001b[39m._build_request_auth(request, auth)\n\u001b[32m--> \u001b[39m\u001b[32m914\u001b[39m response = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_send_handling_auth\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    915\u001b[39m \u001b[43m    \u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    916\u001b[39m \u001b[43m    \u001b[49m\u001b[43mauth\u001b[49m\u001b[43m=\u001b[49m\u001b[43mauth\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    917\u001b[39m \u001b[43m    \u001b[49m\u001b[43mfollow_redirects\u001b[49m\u001b[43m=\u001b[49m\u001b[43mfollow_redirects\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    918\u001b[39m \u001b[43m    \u001b[49m\u001b[43mhistory\u001b[49m\u001b[43m=\u001b[49m\u001b[43m[\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    919\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    920\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m    921\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m stream:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/face-validation/.venv/lib/python3.13/site-packages/httpx/_client.py:942\u001b[39m, in \u001b[36mClient._send_handling_auth\u001b[39m\u001b[34m(self, request, auth, follow_redirects, history)\u001b[39m\n\u001b[32m    939\u001b[39m request = \u001b[38;5;28mnext\u001b[39m(auth_flow)\n\u001b[32m    941\u001b[39m \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m942\u001b[39m     response = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_send_handling_redirects\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    943\u001b[39m \u001b[43m        \u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    944\u001b[39m \u001b[43m        \u001b[49m\u001b[43mfollow_redirects\u001b[49m\u001b[43m=\u001b[49m\u001b[43mfollow_redirects\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    945\u001b[39m \u001b[43m        \u001b[49m\u001b[43mhistory\u001b[49m\u001b[43m=\u001b[49m\u001b[43mhistory\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    946\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    947\u001b[39m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m    948\u001b[39m         \u001b[38;5;28;01mtry\u001b[39;00m:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/face-validation/.venv/lib/python3.13/site-packages/httpx/_client.py:979\u001b[39m, in \u001b[36mClient._send_handling_redirects\u001b[39m\u001b[34m(self, request, follow_redirects, history)\u001b[39m\n\u001b[32m    976\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m hook \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m._event_hooks[\u001b[33m\"\u001b[39m\u001b[33mrequest\u001b[39m\u001b[33m\"\u001b[39m]:\n\u001b[32m    977\u001b[39m     hook(request)\n\u001b[32m--> \u001b[39m\u001b[32m979\u001b[39m response = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_send_single_request\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    980\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m    981\u001b[39m     \u001b[38;5;28;01mfor\u001b[39;00m hook \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m._event_hooks[\u001b[33m\"\u001b[39m\u001b[33mresponse\u001b[39m\u001b[33m\"\u001b[39m]:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/face-validation/.venv/lib/python3.13/site-packages/httpx/_client.py:1014\u001b[39m, in \u001b[36mClient._send_single_request\u001b[39m\u001b[34m(self, request)\u001b[39m\n\u001b[32m   1009\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mRuntimeError\u001b[39;00m(\n\u001b[32m   1010\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33mAttempted to send an async request with a sync Client instance.\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m   1011\u001b[39m     )\n\u001b[32m   1013\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m request_context(request=request):\n\u001b[32m-> \u001b[39m\u001b[32m1014\u001b[39m     response = \u001b[43mtransport\u001b[49m\u001b[43m.\u001b[49m\u001b[43mhandle_request\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1016\u001b[39m \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(response.stream, SyncByteStream)\n\u001b[32m   1018\u001b[39m response.request = request\n", "\u001b[36mFile \u001b[39m\u001b[32m~/face-validation/.venv/lib/python3.13/site-packages/httpx/_transports/default.py:250\u001b[39m, in \u001b[36mHTTPTransport.handle_request\u001b[39m\u001b[34m(self, request)\u001b[39m\n\u001b[32m    237\u001b[39m req = httpcore.Request(\n\u001b[32m    238\u001b[39m     method=request.method,\n\u001b[32m    239\u001b[39m     url=httpcore.URL(\n\u001b[32m   (...)\u001b[39m\u001b[32m    247\u001b[39m     extensions=request.extensions,\n\u001b[32m    248\u001b[39m )\n\u001b[32m    249\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m map_httpcore_exceptions():\n\u001b[32m--> \u001b[39m\u001b[32m250\u001b[39m     resp = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_pool\u001b[49m\u001b[43m.\u001b[49m\u001b[43mhandle_request\u001b[49m\u001b[43m(\u001b[49m\u001b[43mreq\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    252\u001b[39m \u001b[38;5;28;01massert\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(resp.stream, typing.Iterable)\n\u001b[32m    254\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m Response(\n\u001b[32m    255\u001b[39m     status_code=resp.status,\n\u001b[32m    256\u001b[39m     headers=resp.headers,\n\u001b[32m    257\u001b[39m     stream=ResponseStream(resp.stream),\n\u001b[32m    258\u001b[39m     extensions=resp.extensions,\n\u001b[32m    259\u001b[39m )\n", "\u001b[36mFile \u001b[39m\u001b[32m~/face-validation/.venv/lib/python3.13/site-packages/httpcore/_sync/connection_pool.py:256\u001b[39m, in \u001b[36mConnectionPool.handle_request\u001b[39m\u001b[34m(self, request)\u001b[39m\n\u001b[32m    253\u001b[39m         closing = \u001b[38;5;28mself\u001b[39m._assign_requests_to_connections()\n\u001b[32m    255\u001b[39m     \u001b[38;5;28mself\u001b[39m._close_connections(closing)\n\u001b[32m--> \u001b[39m\u001b[32m256\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m exc \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mN<PERSON>\u001b[39;00m\n\u001b[32m    258\u001b[39m \u001b[38;5;66;03m# Return the response. Note that in this case we still have to manage\u001b[39;00m\n\u001b[32m    259\u001b[39m \u001b[38;5;66;03m# the point at which the response is closed.\u001b[39;00m\n\u001b[32m    260\u001b[39m \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(response.stream, typing.Iterable)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/face-validation/.venv/lib/python3.13/site-packages/httpcore/_sync/connection_pool.py:236\u001b[39m, in \u001b[36mConnectionPool.handle_request\u001b[39m\u001b[34m(self, request)\u001b[39m\n\u001b[32m    232\u001b[39m connection = pool_request.wait_for_connection(timeout=timeout)\n\u001b[32m    234\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m    235\u001b[39m     \u001b[38;5;66;03m# Send the request on the assigned connection.\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m236\u001b[39m     response = \u001b[43mconnection\u001b[49m\u001b[43m.\u001b[49m\u001b[43mhandle_request\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    237\u001b[39m \u001b[43m        \u001b[49m\u001b[43mpool_request\u001b[49m\u001b[43m.\u001b[49m\u001b[43mrequest\u001b[49m\n\u001b[32m    238\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    239\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m ConnectionNotAvailable:\n\u001b[32m    240\u001b[39m     \u001b[38;5;66;03m# In some cases a connection may initially be available to\u001b[39;00m\n\u001b[32m    241\u001b[39m     \u001b[38;5;66;03m# handle a request, but then become unavailable.\u001b[39;00m\n\u001b[32m    242\u001b[39m     \u001b[38;5;66;03m#\u001b[39;00m\n\u001b[32m    243\u001b[39m     \u001b[38;5;66;03m# In this case we clear the connection and try again.\u001b[39;00m\n\u001b[32m    244\u001b[39m     pool_request.clear_connection()\n", "\u001b[36mFile \u001b[39m\u001b[32m~/face-validation/.venv/lib/python3.13/site-packages/httpcore/_sync/connection.py:103\u001b[39m, in \u001b[36mHTTPConnection.handle_request\u001b[39m\u001b[34m(self, request)\u001b[39m\n\u001b[32m    100\u001b[39m     \u001b[38;5;28mself\u001b[39m._connect_failed = \u001b[38;5;28;01mTrue\u001b[39;00m\n\u001b[32m    101\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m exc\n\u001b[32m--> \u001b[39m\u001b[32m103\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_connection\u001b[49m\u001b[43m.\u001b[49m\u001b[43mhandle_request\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/face-validation/.venv/lib/python3.13/site-packages/httpcore/_sync/http11.py:136\u001b[39m, in \u001b[36mHTTP11Connection.handle_request\u001b[39m\u001b[34m(self, request)\u001b[39m\n\u001b[32m    134\u001b[39m     \u001b[38;5;28;01mwith\u001b[39;00m Trace(\u001b[33m\"\u001b[39m\u001b[33mresponse_closed\u001b[39m\u001b[33m\"\u001b[39m, logger, request) \u001b[38;5;28;01mas\u001b[39;00m trace:\n\u001b[32m    135\u001b[39m         \u001b[38;5;28mself\u001b[39m._response_closed()\n\u001b[32m--> \u001b[39m\u001b[32m136\u001b[39m \u001b[38;5;28;01mraise\u001b[39;00m exc\n", "\u001b[36mFile \u001b[39m\u001b[32m~/face-validation/.venv/lib/python3.13/site-packages/httpcore/_sync/http11.py:106\u001b[39m, in \u001b[36mHTTP11Connection.handle_request\u001b[39m\u001b[34m(self, request)\u001b[39m\n\u001b[32m     95\u001b[39m     \u001b[38;5;28;01mpass\u001b[39;00m\n\u001b[32m     97\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m Trace(\n\u001b[32m     98\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33mreceive_response_headers\u001b[39m\u001b[33m\"\u001b[39m, logger, request, kwargs\n\u001b[32m     99\u001b[39m ) \u001b[38;5;28;01mas\u001b[39;00m trace:\n\u001b[32m    100\u001b[39m     (\n\u001b[32m    101\u001b[39m         http_version,\n\u001b[32m    102\u001b[39m         status,\n\u001b[32m    103\u001b[39m         reason_phrase,\n\u001b[32m    104\u001b[39m         headers,\n\u001b[32m    105\u001b[39m         trailing_data,\n\u001b[32m--> \u001b[39m\u001b[32m106\u001b[39m     ) = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_receive_response_headers\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    107\u001b[39m     trace.return_value = (\n\u001b[32m    108\u001b[39m         http_version,\n\u001b[32m    109\u001b[39m         status,\n\u001b[32m    110\u001b[39m         reason_phrase,\n\u001b[32m    111\u001b[39m         headers,\n\u001b[32m    112\u001b[39m     )\n\u001b[32m    114\u001b[39m network_stream = \u001b[38;5;28mself\u001b[39m._network_stream\n", "\u001b[36mFile \u001b[39m\u001b[32m~/face-validation/.venv/lib/python3.13/site-packages/httpcore/_sync/http11.py:177\u001b[39m, in \u001b[36mHTTP11Connection._receive_response_headers\u001b[39m\u001b[34m(self, request)\u001b[39m\n\u001b[32m    174\u001b[39m timeout = timeouts.get(\u001b[33m\"\u001b[39m\u001b[33mread\u001b[39m\u001b[33m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m)\n\u001b[32m    176\u001b[39m \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m177\u001b[39m     event = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_receive_event\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m=\u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    178\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(event, h11.Response):\n\u001b[32m    179\u001b[39m         \u001b[38;5;28;01mbreak\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/face-validation/.venv/lib/python3.13/site-packages/httpcore/_sync/http11.py:217\u001b[39m, in \u001b[36mHTTP11Connection._receive_event\u001b[39m\u001b[34m(self, timeout)\u001b[39m\n\u001b[32m    214\u001b[39m     event = \u001b[38;5;28mself\u001b[39m._h11_state.next_event()\n\u001b[32m    216\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m event \u001b[38;5;129;01mis\u001b[39;00m h11.NEED_DATA:\n\u001b[32m--> \u001b[39m\u001b[32m217\u001b[39m     data = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_network_stream\u001b[49m\u001b[43m.\u001b[49m\u001b[43mread\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    218\u001b[39m \u001b[43m        \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mREAD_NUM_BYTES\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m=\u001b[49m\u001b[43mtimeout\u001b[49m\n\u001b[32m    219\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    221\u001b[39m     \u001b[38;5;66;03m# If we feed this case through h11 we'll raise an exception like:\u001b[39;00m\n\u001b[32m    222\u001b[39m     \u001b[38;5;66;03m#\u001b[39;00m\n\u001b[32m    223\u001b[39m     \u001b[38;5;66;03m#     httpcore.RemoteProtocolError: can't handle event type\u001b[39;00m\n\u001b[32m   (...)\u001b[39m\u001b[32m    227\u001b[39m     \u001b[38;5;66;03m# perspective. Instead we handle this case distinctly and treat\u001b[39;00m\n\u001b[32m    228\u001b[39m     \u001b[38;5;66;03m# it as a ConnectError.\u001b[39;00m\n\u001b[32m    229\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m data == \u001b[33mb\u001b[39m\u001b[33m\"\u001b[39m\u001b[33m\"\u001b[39m \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28mself\u001b[39m._h11_state.their_state == h11.SEND_RESPONSE:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/face-validation/.venv/lib/python3.13/site-packages/httpcore/_backends/sync.py:128\u001b[39m, in \u001b[36mSyncStream.read\u001b[39m\u001b[34m(self, max_bytes, timeout)\u001b[39m\n\u001b[32m    126\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m map_exceptions(exc_map):\n\u001b[32m    127\u001b[39m     \u001b[38;5;28mself\u001b[39m._sock.settimeout(timeout)\n\u001b[32m--> \u001b[39m\u001b[32m128\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_sock\u001b[49m\u001b[43m.\u001b[49m\u001b[43mrecv\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmax_bytes\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[31mKeyboardInterrupt\u001b[39m: "]}], "source": ["\n", "print(f\"Listening to topic: {KAFKA_TOPIC_ES_CREATED_FILE}\")\n", "\n", "for message in consumer:\n", "    msg = message.value\n", "    data = None\n", "    try:\n", "        data = FileMessage(**json.loads(msg))\n", "        print(data)\n", "    except Exception:\n", "        pass\n", "    \n", "    if not data or not data.payload.es_id:\n", "        continue\n", "    \n", "    \n", "    file_document = get_document(data.payload.es_id)\n", "    print(file_document)\n", "# else:\n", "#     print(\"<PERSON><PERSON><PERSON> is disabled.\")\n"]}, {"cell_type": "code", "execution_count": 10, "id": "9b7f98fc", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'_id': 'f75a7727163d803ee1b981f6e9487dfc', 'mimeType': 'image/webp'}"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["get_document(\"f75a7727163d803ee1b981f6e9487dfc\", download=False)"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 5}