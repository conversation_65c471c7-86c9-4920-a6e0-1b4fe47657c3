from pydantic import BaseModel, Field
from typing import Optional


class FilePayload(BaseModel):
    """Payload model for file messages from Kafka."""
    
    file_name: Optional[str] = Field(None, alias="fileName")
    es_id: Optional[str] = Field(None, alias="es-id")
    file_type: Optional[str] = Field(None, alias="fileType")


class FileMessage(BaseModel):
    """Kafka message model for file creation events."""
    
    payload: FilePayload
