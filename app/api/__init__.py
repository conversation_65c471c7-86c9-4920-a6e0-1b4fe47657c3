from datetime import datetime

from fastapi import APIRouter
from fastapi.responses import JSONResponse

from app.api.face_routes import face_router
from app.config import (
    BUILD_DATE,
    ENVIRONMENT,
    IMAGE_QUALITY_OPTIMIZATION,
    IMAGE_RESIZE_ENABLED,
    KAFKA_CONSUMER_GROUP,
    KAFKA_ENABLED,
    KAFKA_TOPIC_ES_CREATED_FILE,
    MAX_IMAGE_DIMENSION,
    PRESERVE_ASPECT_RATIO,
    TITLE,
    ULTRA_FAST_PROCESSING,
    VERSION,
)
from app.helpers import get_face_detector_status
from app.services import kafka_consumer_service

UP_TIME = datetime.now()

router = APIRouter()


@router.get("/", include_in_schema=False)
async def root() -> JSONResponse:
    build_time = datetime.fromtimestamp(int(BUILD_DATE))
    build_time_str = build_time.strftime("%Y-%m-%dT%H:%M:%SZ")
    return JSONResponse(
        {
            "build_time": build_time_str,
            "environment": ENVIRONMENT,
            "message": f"Welcome to {TITLE}!",
            "version": f"{VERSION}.{BUILD_DATE}",
            "uptime": f"{(datetime.now() - UP_TIME).total_seconds():.2f} seconds",
        }
    )


@router.get("/health", include_in_schema=False)
async def health_check() -> JSONResponse:
    """Health check endpoint with face detector status."""
    detector_status = get_face_detector_status()

    # Get Kafka consumer status
    kafka_status = {
        "enabled": kafka_consumer_service.is_running,
        "healthy": kafka_consumer_service.is_healthy(),
    }

    # Determine overall health
    is_healthy = detector_status.get("is_ready", False)
    status_code = 200 if is_healthy else 503

    return JSONResponse(
        status_code=status_code,
        content={
            "status": "healthy" if is_healthy else "unhealthy",
            "timestamp": datetime.now().isoformat(),
            "uptime_seconds": (datetime.now() - UP_TIME).total_seconds(),
            "face_detector": detector_status,
            "kafka_consumer": kafka_status,
        },
    )


@router.get("/ready", include_in_schema=False)
async def readiness_check() -> JSONResponse:
    """Readiness check endpoint for Kubernetes/container orchestration."""
    detector_status = get_face_detector_status()

    is_ready = detector_status.get("is_ready", False) and not detector_status.get(
        "is_initializing", False
    )
    status_code = 200 if is_ready else 503

    return JSONResponse(
        status_code=status_code,
        content={
            "ready": is_ready,
            "timestamp": datetime.now().isoformat(),
            "face_detector": {
                "is_ready": detector_status.get("is_ready", False),
                "is_initializing": detector_status.get("is_initializing", False),
            },
        },
    )


@router.get("/config", include_in_schema=False)
async def get_configuration() -> JSONResponse:
    """Get current service configuration."""
    return JSONResponse(
        {
            "service": {
                "title": TITLE,
                "version": f"{VERSION}.{BUILD_DATE}",
                "environment": ENVIRONMENT,
            },
            "image_processing": {
                "max_image_dimension": MAX_IMAGE_DIMENSION,
                "resize_enabled": IMAGE_RESIZE_ENABLED,
                "quality_optimization": IMAGE_QUALITY_OPTIMIZATION,
                "preserve_aspect_ratio": PRESERVE_ASPECT_RATIO,
                "ultra_fast_processing": ULTRA_FAST_PROCESSING,
            },
            "face_detection": get_face_detector_status(),
            "kafka": {
                "enabled": KAFKA_ENABLED,
                "topic": KAFKA_TOPIC_ES_CREATED_FILE,
                "consumer_group": KAFKA_CONSUMER_GROUP,
                "service_status": {
                    "running": kafka_consumer_service.is_running,
                    "healthy": kafka_consumer_service.is_healthy(),
                },
            },
        }
    )


routers = [router, face_router]
