import io
import time
from typing import Optional, <PERSON>ple

import numpy as np
from PIL import Image

from app.helpers import (
    detect_faces_in_image,
    download_image_to_memory,
    is_valid_image_url,
    patch_file_metadata,
)
from app.logging import logger
from app.models import FaceValidationResponse


class FaceValidationService:
    """Service for validating faces in images from URLs."""

    def __init__(self):
        """Initialize the face validation service."""
        logger.info("Face validation service initialized")

    async def validate_face_from_url(
        self, file_url: str, file_id: str
    ) -> FaceValidationResponse:
        """
        Validate if an image from a URL contains faces.

        Args:
            file_url: URL of the image to validate
            file_id: ID of the file (for logging purposes)

        Returns:
            FaceValidationResponse with validation result
        """
        start_time = time.time()

        try:
            # Step 1: Validate URL
            is_valid, url_error = await is_valid_image_url(file_url)
            if not is_valid:
                logger.warning(f"URL validation failed for {file_id}: {url_error}")
                return FaceValidationResponse(
                    has_face=False, error=f"URL validation failed: {url_error}"
                )

            # Step 2: Download image
            image_array, download_error = await download_image_to_memory(file_url)
            if image_array is None:
                logger.warning(f"Image download failed for {file_id}: {download_error}")
                return FaceValidationResponse(
                    has_face=False, error=f"Image download failed: {download_error}"
                )

            # Step 3: Detect faces
            has_faces, detection_error = detect_faces_in_image(image_array)
            if detection_error:
                logger.error(f"Face detection failed for {file_id}: {detection_error}")
                return FaceValidationResponse(
                    has_face=False, error=f"Face detection failed: {detection_error}"
                )

            # Step 4: Return result with timing
            total_time = time.time() - start_time
            logger.info(
                f"Validation complete [{file_id}]: {'✓' if has_faces else '✗'} faces ({total_time:.2f}s)"
            )

            return FaceValidationResponse(
                has_face=has_faces,
            )

        except Exception as e:
            error_msg = (
                f"Unexpected error during face validation for {file_id}: {str(e)}"
            )
            logger.error(error_msg)
            return FaceValidationResponse(has_face=False, error=error_msg)

    def _convert_bytes_to_image_array(
        self, image_bytes: bytes
    ) -> Tuple[Optional[np.ndarray], Optional[str]]:
        """
        Convert image bytes to numpy array.

        Args:
            image_bytes: Raw image bytes

        Returns:
            Tuple of (image_array, error_message). If successful, error_message is None.
        """
        try:
            # Open image from bytes
            image = Image.open(io.BytesIO(image_bytes))

            # Convert to RGB if needed
            if image.mode != "RGB":
                image = image.convert("RGB")

            # Convert to numpy array
            image_array = np.array(image)

            return image_array, None

        except Exception as e:
            error_msg = f"Failed to convert bytes to image array: {str(e)}"
            return None, error_msg

    async def validate_face_from_bytes(
        self, image_bytes: bytes, file_id: str, source: str = "kafka"
    ) -> FaceValidationResponse:
        """
        Validate if image bytes contain faces.

        Args:
            image_bytes: Raw image bytes to validate
            file_id: ID of the file (for logging purposes)
            source: Source of the request (for logging, default: "kafka")

        Returns:
            FaceValidationResponse with validation result
        """
        start_time = time.time()

        try:
            # Log request with source tag
            logger.info(f"[{source.upper()}] Face validation request - ID: {file_id}")

            # Step 1: Convert bytes to image array
            image_array, conversion_error = self._convert_bytes_to_image_array(
                image_bytes
            )
            if image_array is None:
                logger.warning(
                    f"[{source.upper()}] Image conversion failed for {file_id}: {conversion_error}"
                )
                return FaceValidationResponse(
                    has_face=False, error=f"Image conversion failed: {conversion_error}"
                )

            # Step 2: Detect faces
            has_faces, detection_error = detect_faces_in_image(image_array)
            if detection_error:
                logger.error(
                    f"[{source.upper()}] Face detection failed for {file_id}: {detection_error}"
                )
                return FaceValidationResponse(
                    has_face=False, error=f"Face detection failed: {detection_error}"
                )

            # Step 3: Update docserver with face detection result
            patch_success = await patch_file_metadata(file_id, has_faces)
            if not patch_success:
                logger.warning(
                    f"[{source.upper()}] Failed to update docserver metadata for {file_id}"
                )

            # Step 4: Return result with timing
            total_time = time.time() - start_time
            logger.info(
                f"[{source.upper()}] Validation complete [{file_id}]: {'✓' if has_faces else '✗'} faces ({total_time:.2f}s)"
            )

            return FaceValidationResponse(
                has_face=has_faces,
            )

        except Exception as e:
            error_msg = (
                f"Unexpected error during face validation for {file_id}: {str(e)}"
            )
            logger.error(f"[{source.upper()}] {error_msg}")
            return FaceValidationResponse(has_face=False, error=error_msg)


# Global service instance
face_validation_service = FaceValidationService()
