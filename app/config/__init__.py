import os

from dotenv import find_dotenv, load_dotenv

try:
    load_dotenv(find_dotenv(".env"))
    load_dotenv(find_dotenv("thisversion.env"))
except Exception as e:
    pass

# SERVICE CONFIG
TITLE = "Image Facial Validation Service"
DESCRIPTION = "API for validating whether an image has faces or not."

ROOT_PATH = os.getenv("ROOT_PATH", "")
LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")

VERSION = os.getenv("VERSION", "0.0.0")
BUILD_DATE = os.getenv("TIMESTAMP", "0")
ENVIRONMENT = os.getenv("ENVIRONMENT", "development").lower()


# API CONFIG
HOST = os.getenv("HOST", "0.0.0.0")
PORT = int(os.getenv("PORT", 8000))
DEBUG_MODULES = os.getenv(
    "DEBUG_MODULES", "app.api,app.services,watchfiles.main"
).split(",")

# FACE DETECTION CONFIG
PRELOAD_MODEL = os.getenv("PRELOAD_MODEL", "true").lower() == "true"
MODEL_DETECTION_SIZE = int(os.getenv("MODEL_DETECTION_SIZE", "640"))
MODEL_WARMUP_ENABLED = os.getenv("MODEL_WARMUP_ENABLED", "true").lower() == "true"

# IMAGE PROCESSING CONFIG
MAX_IMAGE_DIMENSION = int(os.getenv("MAX_IMAGE_DIMENSION", "1024"))
IMAGE_RESIZE_ENABLED = os.getenv("IMAGE_RESIZE_ENABLED", "true").lower() == "true"
IMAGE_QUALITY_OPTIMIZATION = (
    os.getenv("IMAGE_QUALITY_OPTIMIZATION", "true").lower() == "true"
)
PRESERVE_ASPECT_RATIO = os.getenv("PRESERVE_ASPECT_RATIO", "true").lower() == "true"
ULTRA_FAST_PROCESSING = os.getenv("ULTRA_FAST_PROCESSING", "true").lower() == "true"

# HTTP CLIENT CONFIG
HTTP_TIMEOUT_SECONDS = int(os.getenv("HTTP_TIMEOUT_SECONDS", "30"))
HTTP_CONNECT_TIMEOUT_SECONDS = int(os.getenv("HTTP_CONNECT_TIMEOUT_SECONDS", "10"))
HTTP_READ_TIMEOUT_SECONDS = int(os.getenv("HTTP_READ_TIMEOUT_SECONDS", "30"))
HTTP_POOL_CONNECTIONS = int(os.getenv("HTTP_POOL_CONNECTIONS", "10"))
HTTP_POOL_MAXSIZE = int(os.getenv("HTTP_POOL_MAXSIZE", "20"))
HTTP_MAX_REDIRECTS = int(os.getenv("HTTP_MAX_REDIRECTS", "5"))

# CACHING CONFIG
CACHE_ENABLED = os.getenv("CACHE_ENABLED", "true").lower() == "true"
CACHE_TYPE = os.getenv("CACHE_TYPE", "memory")  # memory, disk, or hybrid
CACHE_MAX_SIZE_MB = int(os.getenv("CACHE_MAX_SIZE_MB", "500"))
CACHE_TTL_SECONDS = int(os.getenv("CACHE_TTL_SECONDS", "3600"))  # 1 hour default
CACHE_IMAGE_DOWNLOADS = os.getenv("CACHE_IMAGE_DOWNLOADS", "true").lower() == "true"
CACHE_FACE_DETECTION = os.getenv("CACHE_FACE_DETECTION", "true").lower() == "true"
CACHE_IMAGE_PROCESSING = os.getenv("CACHE_IMAGE_PROCESSING", "true").lower() == "true"
CACHE_DIRECTORY = os.getenv("CACHE_DIRECTORY", "./cache")

# KAFKA CONFIG
KAFKA_ENABLED = os.getenv("KAFKA_ENABLED", "true").lower() == "true"
KAFKA_URL = os.getenv("KAFKA_URL", "kafka.default.svc.cluster.local:9092")
KAFKA_TOPIC_ES_CREATED_FILE = os.getenv(
    "KAFKA_TOPIC_ES_CREATED_FILE", "es_created_File"
)
KAFKA_CONSUMER_GROUP = os.getenv("KAFKA_CONSUMER_GROUP", "face-validation")
KAFKA_AUTO_OFFSET_RESET = os.getenv("KAFKA_AUTO_OFFSET_RESET", "latest")

# DOCSERVER CONFIG
DOCSERVER_V2_BASEURL = os.getenv(
    "DOCSERVER_V2_URL", "http://cyber-docserver-v2-microservice.cyber.svc.cluster.local"
)
DOCSERVER_MAX_RETRIES = int(os.getenv("DOCSERVER_MAX_RETRIES", "3"))
DOCSERVER_RETRY_BACKOFF_FACTOR = float(
    os.getenv("DOCSERVER_RETRY_BACKOFF_FACTOR", "2.0")
)
DOCSERVER_RETRY_MAX_DELAY = int(os.getenv("DOCSERVER_RETRY_MAX_DELAY", "30"))
