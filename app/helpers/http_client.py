import asyncio

import httpx

from app.config import (
    HTTP_CONNECT_TIMEOUT_SECONDS,
    HTTP_MAX_REDIRECTS,
    HTTP_READ_TIMEOUT_SECONDS,
    HTTP_TIMEOUT_SECONDS,
)
from app.logging import logger


def create_http_client() -> httpx.AsyncClient:
    """Create a new HTTP client with proper timeouts and settings."""
    timeout = httpx.Timeout(
        connect=HTTP_CONNECT_TIMEOUT_SECONDS,
        read=HTTP_READ_TIMEOUT_SECONDS,
        write=HTTP_TIMEOUT_SECONDS,
        pool=HTTP_TIMEOUT_SECONDS,
    )

    return httpx.AsyncClient(
        timeout=timeout,
        follow_redirects=True,
        max_redirects=HTTP_MAX_REDIRECTS,
        headers={
            "User-Agent": "Face-Validation-Service/1.0",
        },
    )


async def make_http_request_with_retry(
    request_func, max_retries: int = 3, backoff_factor: float = 0.5
):
    """
    Make an HTTP request with automatic retries on connection errors.

    Args:
        request_func: Async function that takes an httpx.AsyncClient and returns a response
        max_retries: Maximum number of retries
        backoff_factor: Delay multiplier between retries

    Returns:
        The response from the request function

    Raises:
        The last exception if all retries fail
    """
    last_exception = None

    for attempt in range(max_retries + 1):
        client = None
        try:
            client = create_http_client()
            return await request_func(client)
        except (
            httpx.TimeoutException,
            httpx.ConnectError,
            httpx.NetworkError,
            httpx.RemoteProtocolError,
            httpx.LocalProtocolError,
            httpx.PoolTimeout,
            httpx.ReadTimeout,
            httpx.WriteTimeout,
            httpx.ConnectTimeout,
            ConnectionError,
            OSError,
        ) as e:
            last_exception = e
            if attempt == max_retries:
                break

            delay = backoff_factor * (2**attempt)
            logger.warning(
                f"HTTP request failed (attempt {attempt + 1}/{max_retries + 1}): {str(e)}. "
                f"Retrying in {delay:.1f}s..."
            )
            await asyncio.sleep(delay)
        finally:
            if client:
                try:
                    await client.aclose()
                except Exception:
                    pass  # Ignore errors when closing

    # If we get here, all retries failed
    if last_exception:
        raise last_exception
    else:
        raise Exception("All HTTP request retries failed")


# Legacy functions for backward compatibility
async def get_http_client() -> httpx.AsyncClient:
    """Get a new HTTP client. For backward compatibility."""
    return create_http_client()


async def close_http_client() -> None:
    """Close HTTP client. For backward compatibility - no-op since we don't use singleton."""
    pass
