from contextlib import asynccontextmanager

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from app.api import routers
from app.config import (
    BUILD_DATE,
    DESCRIPTION,
    HOST,
    PORT,
    PRELOAD_MODEL,
    TITLE,
    VERSION,
)
from app.helpers import close_http_client, preload_face_detector
from app.logging import init_logger, logger
from app.services import kafka_consumer_service

init_logger()


@asynccontextmanager
async def lifespan(_app: FastAPI):
    """Handle application startup and shutdown events."""
    # Startup
    logger.info("Starting face validation service...")

    # Preload the face detection model to avoid cold start delays
    if PRELOAD_MODEL:
        logger.info("Preloading face detection model...")
        if preload_face_detector():
            logger.info("Face detection model preloaded successfully")
        else:
            logger.warning(
                "Failed to preload face detection model - will load on first request"
            )
    else:
        logger.info("Model preloading disabled - model will load on first request")

    # Start Kafka consumer service
    if kafka_consumer_service.start():
        logger.info("Kafka consumer service started successfully")
    else:
        logger.warning("Kafka consumer service failed to start or is disabled")

    yield

    # Shutdown
    logger.info("Shutting down face validation service...")

    # Stop Kafka consumer service first to prevent new processing
    logger.info("Stopping Kafka consumer service...")
    kafka_consumer_service.stop()
    logger.info("Kafka consumer service stopped")

    # Give a brief moment for any remaining HTTP operations to complete
    import asyncio

    await asyncio.sleep(1)

    # Close HTTP client connections
    logger.info("Closing HTTP client connections...")
    await close_http_client()
    logger.info("HTTP client connections closed")

    logger.info("Face validation service shutdown complete")


app = FastAPI(
    title=TITLE,
    version=f"{VERSION}.{BUILD_DATE}",
    description=DESCRIPTION,
    swagger_ui_parameters={"defaultModelsExpandDepth": 0},
    lifespan=lifespan,
)

for router in routers:
    app.include_router(router)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host=HOST, port=PORT)
