# This file was autogenerated by uv via the following command:
#    uv export --no-hashes --format requirements-txt
albucore==0.0.24
    # via albumentations
albumentations==2.0.8
    # via insightface
annotated-types==0.7.0
    # via pydantic
anyio==4.9.0
    # via
    #   fast-depends
    #   faststream
    #   httpx
    #   starlette
appnope==0.1.4 ; sys_platform == 'darwin'
    # via ipykernel
asttokens==3.0.0
    # via stack-data
certifi==2025.4.26
    # via
    #   httpcore
    #   httpx
    #   requests
cffi==1.17.1 ; implementation_name == 'pypy'
    # via pyzmq
charset-normalizer==3.4.2
    # via requests
click==8.2.1
    # via uvicorn
colorama==0.4.6 ; sys_platform == 'win32'
    # via
    #   click
    #   ipython
    #   loguru
    #   pytest
    #   tqdm
coloredlogs==15.0.1
    # via onnxruntime
comm==0.2.2
    # via ipykernel
contourpy==1.3.2
    # via matplotlib
cycler==0.12.1
    # via matplotlib
cython==3.1.1
    # via insightface
debugpy==1.8.14
    # via ipykernel
decorator==5.2.1
    # via ipython
easydict==1.13
    # via insightface
executing==2.2.0
    # via stack-data
fast-depends==2.4.12
    # via faststream
fastapi==0.115.12
    # via face-validation
faststream==0.5.42
    # via face-validation
flatbuffers==25.2.10
    # via onnxruntime
fonttools==4.58.1
    # via matplotlib
h11==0.16.0
    # via
    #   httpcore
    #   uvicorn
httpcore==1.0.9
    # via httpx
httptools==0.6.4
    # via face-validation
httpx==0.28.1
    # via face-validation
humanfriendly==10.0
    # via coloredlogs
idna==3.10
    # via
    #   anyio
    #   httpx
    #   requests
imageio==2.37.0
    # via scikit-image
iniconfig==2.1.0
    # via pytest
insightface==0.7.3
    # via face-validation
ipykernel==6.29.5
ipython==9.2.0
    # via ipykernel
ipython-pygments-lexers==1.1.1
    # via ipython
jedi==0.19.2
    # via ipython
joblib==1.5.1
    # via scikit-learn
jupyter-client==8.6.3
    # via ipykernel
jupyter-core==5.8.1
    # via
    #   ipykernel
    #   jupyter-client
kafka-python==2.2.10
    # via face-validation
kiwisolver==1.4.8
    # via matplotlib
lazy-loader==0.4
    # via scikit-image
loguru==0.7.3
    # via face-validation
matplotlib==3.10.3
    # via insightface
matplotlib-inline==0.1.7
    # via
    #   ipykernel
    #   ipython
mpmath==1.3.0
    # via sympy
nest-asyncio==1.6.0
    # via ipykernel
networkx==3.5
    # via scikit-image
numpy==2.2.6
    # via
    #   albucore
    #   albumentations
    #   contourpy
    #   imageio
    #   insightface
    #   matplotlib
    #   onnx
    #   onnxruntime
    #   opencv-python
    #   opencv-python-headless
    #   scikit-image
    #   scikit-learn
    #   scipy
    #   tifffile
onnx==1.18.0
    # via insightface
onnxruntime==1.22.0
    # via face-validation
opencv-python==*********
    # via face-validation
opencv-python-headless==*********
    # via
    #   albucore
    #   albumentations
packaging==25.0
    # via
    #   ipykernel
    #   lazy-loader
    #   matplotlib
    #   onnxruntime
    #   pytest
    #   scikit-image
parso==0.8.4
    # via jedi
pexpect==4.9.0 ; sys_platform != 'emscripten' and sys_platform != 'win32'
    # via ipython
pillow==11.2.1
    # via
    #   face-validation
    #   imageio
    #   insightface
    #   matplotlib
    #   scikit-image
platformdirs==4.3.8
    # via jupyter-core
pluggy==1.6.0
    # via pytest
prettytable==3.16.0
    # via insightface
prompt-toolkit==3.0.51
    # via ipython
protobuf==6.31.1
    # via
    #   onnx
    #   onnxruntime
psutil==7.0.0
    # via ipykernel
ptyprocess==0.7.0 ; sys_platform != 'emscripten' and sys_platform != 'win32'
    # via pexpect
pure-eval==0.2.3
    # via stack-data
pycparser==2.22 ; implementation_name == 'pypy'
    # via cffi
pydantic==2.11.5
    # via
    #   albumentations
    #   fast-depends
    #   fastapi
pydantic-core==2.33.2
    # via pydantic
pygments==2.19.1
    # via
    #   ipython
    #   ipython-pygments-lexers
pyparsing==3.2.3
    # via matplotlib
pyreadline3==3.5.4 ; sys_platform == 'win32'
    # via humanfriendly
pytest==8.3.5
    # via
    #   face-validation
    #   pytest-asyncio
pytest-asyncio==1.0.0
    # via face-validation
python-dateutil==2.9.0.post0
    # via
    #   jupyter-client
    #   matplotlib
python-dotenv==1.1.0
    # via face-validation
pywin32==310 ; platform_python_implementation != 'PyPy' and sys_platform == 'win32'
    # via jupyter-core
pyyaml==6.0.2
    # via albumentations
pyzmq==26.4.0
    # via
    #   ipykernel
    #   jupyter-client
requests==2.32.3
    # via insightface
scikit-image==0.25.2
    # via insightface
scikit-learn==1.6.1
    # via insightface
scipy==1.15.3
    # via
    #   albumentations
    #   insightface
    #   scikit-image
    #   scikit-learn
simsimd==6.2.1
    # via albucore
six==1.17.0
    # via python-dateutil
sniffio==1.3.1
    # via anyio
stack-data==0.6.3
    # via ipython
starlette==0.46.2
    # via fastapi
stringzilla==3.12.5
    # via albucore
sympy==1.14.0
    # via onnxruntime
threadpoolctl==3.6.0
    # via scikit-learn
tifffile==2025.5.26
    # via scikit-image
tornado==6.5.1
    # via
    #   ipykernel
    #   jupyter-client
tqdm==4.67.1
    # via insightface
traitlets==5.14.3
    # via
    #   comm
    #   ipykernel
    #   ipython
    #   jupyter-client
    #   jupyter-core
    #   matplotlib-inline
typing-extensions==4.13.2
    # via
    #   fastapi
    #   faststream
    #   onnx
    #   pydantic
    #   pydantic-core
    #   typing-inspection
typing-inspection==0.4.1
    # via pydantic
urllib3==2.4.0
    # via requests
uvicorn==0.34.2
    # via face-validation
uvloop==0.21.0
    # via face-validation
wcwidth==0.2.13
    # via
    #   prettytable
    #   prompt-toolkit
win32-setctime==1.2.0 ; sys_platform == 'win32'
    # via loguru
